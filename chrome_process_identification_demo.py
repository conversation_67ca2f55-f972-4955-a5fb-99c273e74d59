#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome进程识别逻辑详细演示
展示如何区分个人Chrome和程序Chrome
"""

import os
import psutil
from datetime import datetime

def analyze_chrome_process_identification():
    """详细分析Chrome进程识别逻辑"""
    print("🔍 Chrome进程识别逻辑详细分析")
    print("=" * 80)
    
    current_dir = os.getcwd().replace('\\', '/')
    
    # 定义识别特征
    program_indicators = [
        'browser_profiles',           # 程序使用的用户数据目录
        'account_',                   # 程序账号目录标识
        'remote-debugging-port=9',    # 程序使用的调试端口
        '--user-data-dir=' + current_dir,  # 程序指定的数据目录
        '--disable-blink-features=AutomationControlled',  # 自动化标识
        '--test-type',                # 测试模式
        '--enable-automation',        # 启用自动化
        '--no-first-run',            # 跳过首次运行
        '--disable-default-apps',     # 禁用默认应用
        '--disable-popup-blocking'    # 禁用弹窗阻止
    ]
    
    personal_indicators = [
        'Default/User Data',          # 个人Chrome默认数据目录
        'Google/Chrome/User Data',    # 个人Chrome用户数据
        '--profile-directory=Default', # 默认配置文件目录
        'AppData/Local/Google/Chrome', # Windows个人Chrome路径
        'Application Data/Google/Chrome', # 旧版Windows路径
        '--no-sandbox',               # 个人Chrome常用参数（但程序也可能用）
    ]
    
    # 保护性特征（绝对不能清理的）
    protected_indicators = [
        'chrome://newtab',            # 新标签页
        'chrome://settings',          # 设置页面
        'chrome://extensions',        # 扩展页面
        '--type=renderer',            # 渲染进程（可能是个人浏览器的）
        'Profile 1',                  # 个人配置文件
        'Profile 2',                  # 个人配置文件
    ]
    
    print("📋 识别特征定义:")
    print("\n🤖 程序Chrome特征 (满足任一条件即认为是程序Chrome):")
    for i, indicator in enumerate(program_indicators, 1):
        print(f"  {i:2d}. {indicator}")
    
    print("\n👤 个人Chrome特征 (满足任一条件即认为是个人Chrome):")
    for i, indicator in enumerate(personal_indicators, 1):
        print(f"  {i:2d}. {indicator}")
    
    print("\n🛡️  保护性特征 (绝对不清理):")
    for i, indicator in enumerate(protected_indicators, 1):
        print(f"  {i:2d}. {indicator}")
    
    print(f"\n" + "=" * 80)
    print("🔍 当前Chrome进程分析")
    print("=" * 80)
    
    chrome_processes = []
    
    # 收集所有Chrome进程
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'memory_info']):
        try:
            if not proc.info['name'] or 'chrome' not in proc.info['name'].lower():
                continue
            
            cmdline = proc.info['cmdline'] or []
            cmdline_str = ' '.join(cmdline)
            
            # 分析每个进程
            process_analysis = {
                'pid': proc.info['pid'],
                'cmdline': cmdline_str,
                'create_time': datetime.fromtimestamp(proc.info['create_time']),
                'memory_mb': proc.info['memory_info'].rss / (1024 * 1024) if proc.info['memory_info'] else 0,
                'program_matches': [],
                'personal_matches': [],
                'protected_matches': [],
                'classification': 'unknown',
                'action': 'unknown'
            }
            
            # 检查程序特征
            for indicator in program_indicators:
                if indicator in cmdline_str:
                    process_analysis['program_matches'].append(indicator)
            
            # 检查个人特征
            for indicator in personal_indicators:
                if indicator in cmdline_str:
                    process_analysis['personal_matches'].append(indicator)
            
            # 检查保护特征
            for indicator in protected_indicators:
                if indicator in cmdline_str:
                    process_analysis['protected_matches'].append(indicator)
            
            # 进行分类
            has_program_features = len(process_analysis['program_matches']) > 0
            has_personal_features = len(process_analysis['personal_matches']) > 0
            has_protected_features = len(process_analysis['protected_matches']) > 0
            
            if has_protected_features:
                process_analysis['classification'] = 'protected'
                process_analysis['action'] = '🛡️  绝对保护'
            elif has_personal_features:
                process_analysis['classification'] = 'personal'
                process_analysis['action'] = '👤 保护（个人Chrome）'
            elif has_program_features:
                process_analysis['classification'] = 'program'
                process_analysis['action'] = '🤖 可清理（程序Chrome）'
            else:
                process_analysis['classification'] = 'unknown'
                process_analysis['action'] = '❓ 未知（保守保护）'
            
            chrome_processes.append(process_analysis)
            
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # 显示分析结果
    if not chrome_processes:
        print("✅ 没有发现Chrome进程")
        return
    
    print(f"发现 {len(chrome_processes)} 个Chrome进程:")
    print()
    
    for i, proc in enumerate(chrome_processes, 1):
        print(f"进程 {i}:")
        print(f"  PID: {proc['pid']}")
        print(f"  内存: {proc['memory_mb']:.1f} MB")
        print(f"  创建时间: {proc['create_time'].strftime('%H:%M:%S')}")
        print(f"  分类: {proc['classification']}")
        print(f"  处理: {proc['action']}")
        
        # 显示命令行（截断显示）
        cmdline_display = proc['cmdline'][:100] + '...' if len(proc['cmdline']) > 100 else proc['cmdline']
        print(f"  命令行: {cmdline_display}")
        
        # 显示匹配的特征
        if proc['program_matches']:
            print(f"  🤖 程序特征: {', '.join(proc['program_matches'])}")
        
        if proc['personal_matches']:
            print(f"  👤 个人特征: {', '.join(proc['personal_matches'])}")
        
        if proc['protected_matches']:
            print(f"  🛡️  保护特征: {', '.join(proc['protected_matches'])}")
        
        if not proc['program_matches'] and not proc['personal_matches'] and not proc['protected_matches']:
            print(f"  ❓ 无明显特征")
        
        print()
    
    # 统计分类结果
    classification_stats = {}
    for proc in chrome_processes:
        classification = proc['classification']
        if classification not in classification_stats:
            classification_stats[classification] = 0
        classification_stats[classification] += 1
    
    print("=" * 80)
    print("📊 分类统计")
    print("=" * 80)
    
    for classification, count in classification_stats.items():
        emoji_map = {
            'program': '🤖',
            'personal': '👤',
            'protected': '🛡️',
            'unknown': '❓'
        }
        emoji = emoji_map.get(classification, '❓')
        print(f"{emoji} {classification}: {count} 个")
    
    # 显示清理决策
    cleanable_count = classification_stats.get('program', 0)
    protected_count = sum(classification_stats.get(key, 0) for key in ['personal', 'protected', 'unknown'])
    
    print(f"\n🔧 清理决策:")
    print(f"  可清理: {cleanable_count} 个程序Chrome进程")
    print(f"  保护: {protected_count} 个非程序Chrome进程")
    
    if cleanable_count > 0:
        print(f"\n⚠️  注意: 只有明确标识为程序Chrome的进程才会被清理")
        print(f"  这确保了你的个人Chrome浏览器不会被误杀")

def demonstrate_identification_logic():
    """演示识别逻辑的工作原理"""
    print(f"\n" + "=" * 80)
    print("🎯 识别逻辑工作原理")
    print("=" * 80)
    
    print("识别流程:")
    print("1. 获取Chrome进程的完整命令行参数")
    print("2. 检查是否包含程序特征")
    print("3. 检查是否包含个人特征")
    print("4. 检查是否包含保护特征")
    print("5. 根据特征组合进行分类")
    print()
    
    print("分类规则:")
    print("  🛡️  保护特征存在 → 绝对保护（不清理）")
    print("  👤 个人特征存在 → 保护（个人Chrome）")
    print("  🤖 仅程序特征存在 → 可清理（程序Chrome）")
    print("  ❓ 无明显特征 → 保守保护（不清理）")
    print()
    
    print("安全原则:")
    print("  ✅ 宁可漏掉程序进程，也不误杀个人进程")
    print("  ✅ 只清理明确标识为程序的Chrome进程")
    print("  ✅ 对不确定的进程采取保护策略")

def show_example_command_lines():
    """显示典型的命令行示例"""
    print(f"\n" + "=" * 80)
    print("📝 典型命令行示例")
    print("=" * 80)
    
    examples = [
        {
            "type": "程序Chrome",
            "cmdline": "chrome.exe --user-data-dir=D:/project/data/browser_profiles/account_10 --remote-debugging-port=9000 --disable-blink-features=AutomationControlled",
            "reason": "包含 browser_profiles, account_, remote-debugging-port=9, --disable-blink-features"
        },
        {
            "type": "个人Chrome",
            "cmdline": "chrome.exe --profile-directory=Default --user-data-dir=C:/Users/<USER>/AppData/Local/Google/Chrome/User Data",
            "reason": "包含 --profile-directory=Default, Google/Chrome/User Data"
        },
        {
            "type": "个人Chrome渲染进程",
            "cmdline": "chrome.exe --type=renderer --no-sandbox --user-data-dir=C:/Users/<USER>/AppData/Local/Google/Chrome/User Data",
            "reason": "包含 Google/Chrome/User Data（个人特征）"
        },
        {
            "type": "保护进程",
            "cmdline": "chrome.exe --type=renderer --extension-process chrome://newtab",
            "reason": "包含 chrome://newtab（保护特征）"
        }
    ]
    
    for example in examples:
        print(f"类型: {example['type']}")
        print(f"命令行: {example['cmdline']}")
        print(f"识别原因: {example['reason']}")
        print()

def main():
    """主函数"""
    try:
        analyze_chrome_process_identification()
        demonstrate_identification_logic()
        show_example_command_lines()
        
        print("=" * 80)
        print("💡 总结")
        print("=" * 80)
        print("Chrome进程识别基于命令行参数中的特征字符串")
        print("程序使用多重安全检查确保只清理程序启动的Chrome进程")
        print("你的个人Chrome浏览器会被完全保护，不会被误清理")
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
