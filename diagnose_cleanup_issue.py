#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断程序退出时Chrome进程清理问题
"""

import os
import sys
import time
import psutil
import threading
import asyncio
from pathlib import Path

def analyze_cleanup_code():
    """分析清理代码的问题"""
    print("🔍 分析程序清理代码...")
    print("=" * 60)
    
    issues = []
    
    # 检查主要清理文件
    cleanup_files = [
        "src/ui/main_window.py",
        "main.py", 
        "src/core/browser_manager.py"
    ]
    
    for file_path in cleanup_files:
        if os.path.exists(file_path):
            print(f"✅ 找到清理文件: {file_path}")
            
            # 读取文件内容分析
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键清理方法
                if 'closeEvent' in content:
                    print(f"  📝 {file_path} 包含 closeEvent 方法")
                
                if '_force_cleanup_chrome_processes' in content:
                    print(f"  🧹 {file_path} 包含强制清理方法")
                
                if 'browser_pool.close_all()' in content:
                    print(f"  🌐 {file_path} 包含浏览器池清理")
                
                if 'terminate()' in content:
                    print(f"  ⚡ {file_path} 包含进程终止代码")
                    
            except Exception as e:
                print(f"  ❌ 读取文件失败: {e}")
        else:
            print(f"❌ 清理文件不存在: {file_path}")
            issues.append(f"缺少清理文件: {file_path}")
    
    return issues

def test_cleanup_timing():
    """测试清理时机问题"""
    print(f"\n🕐 测试清理时机...")
    print("=" * 60)
    
    issues = []
    
    # 模拟程序退出流程
    print("模拟程序退出流程:")
    
    # 1. 检查事件循环问题
    print("1. 检查事件循环状态...")
    try:
        # 检查是否有活跃的事件循环
        try:
            loop = asyncio.get_running_loop()
            print("  ⚠️  发现活跃的事件循环")
            issues.append("退出时存在活跃的事件循环，可能导致异步清理失败")
        except RuntimeError:
            print("  ✅ 没有活跃的事件循环")
    except Exception as e:
        print(f"  ❌ 检查事件循环失败: {e}")
    
    # 2. 检查线程状态
    print("2. 检查线程状态...")
    active_threads = threading.active_count()
    print(f"  活跃线程数: {active_threads}")
    
    if active_threads > 2:  # 主线程 + 可能的守护线程
        print("  ⚠️  存在多个活跃线程")
        issues.append("退出时存在多个活跃线程，可能影响清理")
        
        # 列出所有线程
        for thread in threading.enumerate():
            print(f"    - {thread.name}: {'守护' if thread.daemon else '非守护'}")
    
    # 3. 检查清理超时问题
    print("3. 检查清理超时设置...")
    
    # 从代码中可以看到清理超时设置
    timeouts = {
        "浏览器池清理": "10秒",
        "清理线程等待": "5秒",
        "整体清理": "无限制"
    }
    
    for operation, timeout in timeouts.items():
        print(f"  {operation}: {timeout}")
        if timeout == "无限制":
            issues.append(f"{operation}没有超时限制，可能导致程序挂起")
    
    return issues

def test_process_cleanup_effectiveness():
    """测试进程清理的有效性"""
    print(f"\n🧪 测试进程清理有效性...")
    print("=" * 60)
    
    issues = []
    
    # 检查当前Chrome进程
    chrome_processes_before = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
        try:
            if 'chrome' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'] or [])
                chrome_processes_before.append({
                    'pid': proc.info['pid'],
                    'cmdline': cmdline,
                    'create_time': proc.info['create_time'],
                    'is_program': any(indicator in cmdline for indicator in [
                        'browser_profiles', 'account_', 'remote-debugging-port=9'
                    ])
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    program_chrome_count = sum(1 for p in chrome_processes_before if p['is_program'])
    personal_chrome_count = len(chrome_processes_before) - program_chrome_count
    
    print(f"当前Chrome进程状态:")
    print(f"  程序Chrome进程: {program_chrome_count} 个")
    print(f"  个人Chrome进程: {personal_chrome_count} 个")
    
    if program_chrome_count > 0:
        print(f"  ⚠️  发现程序Chrome进程残留")
        issues.append("存在程序Chrome进程残留，说明之前的清理不完整")
        
        # 分析残留进程的特征
        for proc in chrome_processes_before:
            if proc['is_program']:
                age = time.time() - proc['create_time']
                print(f"    PID {proc['pid']}: 存活 {age/60:.1f} 分钟")
    
    return issues

def analyze_cleanup_race_conditions():
    """分析清理过程中的竞态条件"""
    print(f"\n🏃 分析清理竞态条件...")
    print("=" * 60)
    
    issues = []
    
    # 1. 异步清理 vs 同步退出
    print("1. 异步清理 vs 同步退出:")
    print("  问题: closeEvent是同步的，但浏览器清理是异步的")
    print("  风险: 程序可能在清理完成前就退出")
    issues.append("异步清理与同步退出存在竞态条件")
    
    # 2. 多线程清理
    print("2. 多线程清理:")
    print("  问题: 清理在新线程中执行，主线程可能提前退出")
    print("  风险: 清理线程可能被强制终止")
    issues.append("多线程清理可能被主线程提前退出打断")
    
    # 3. 事件循环冲突
    print("3. 事件循环冲突:")
    print("  问题: 清理时创建新事件循环，可能与现有循环冲突")
    print("  风险: 异步操作可能失败")
    issues.append("事件循环管理可能存在冲突")
    
    # 4. 进程保护机制
    print("4. 进程保护机制:")
    print("  问题: 保护机制可能阻止清理程序进程")
    print("  风险: 程序进程被误认为个人进程而保护")
    issues.append("进程保护机制可能过于保守")
    
    return issues

def suggest_improvements():
    """建议改进方案"""
    print(f"\n💡 改进建议...")
    print("=" * 60)
    
    improvements = [
        {
            "问题": "异步清理竞态条件",
            "解决方案": [
                "在closeEvent中使用同步等待异步清理完成",
                "增加强制清理作为备选方案",
                "设置合理的清理超时时间"
            ]
        },
        {
            "问题": "多线程清理被打断",
            "解决方案": [
                "使用daemon=False确保清理线程完成",
                "增加清理完成的确认机制",
                "在主线程中执行关键清理步骤"
            ]
        },
        {
            "问题": "事件循环冲突",
            "解决方案": [
                "检查现有事件循环状态",
                "使用统一的事件循环管理",
                "提供同步清理的备选方案"
            ]
        },
        {
            "问题": "进程识别不准确",
            "解决方案": [
                "改进程序Chrome进程的识别逻辑",
                "使用更精确的进程标识符",
                "记录程序启动的进程ID"
            ]
        },
        {
            "问题": "清理超时处理",
            "解决方案": [
                "为所有清理操作设置超时",
                "提供强制清理的降级方案",
                "记录清理失败的详细信息"
            ]
        }
    ]
    
    for i, improvement in enumerate(improvements, 1):
        print(f"{i}. {improvement['问题']}:")
        for solution in improvement['解决方案']:
            print(f"   - {solution}")
        print()

def main():
    """主诊断函数"""
    print("🔧 Chrome进程清理问题诊断")
    print("=" * 80)
    
    all_issues = []
    
    # 1. 分析清理代码
    code_issues = analyze_cleanup_code()
    all_issues.extend(code_issues)
    
    # 2. 测试清理时机
    timing_issues = test_cleanup_timing()
    all_issues.extend(timing_issues)
    
    # 3. 测试进程清理有效性
    effectiveness_issues = test_process_cleanup_effectiveness()
    all_issues.extend(effectiveness_issues)
    
    # 4. 分析竞态条件
    race_issues = analyze_cleanup_race_conditions()
    all_issues.extend(race_issues)
    
    # 总结问题
    print(f"\n" + "=" * 80)
    print("📊 问题总结")
    print("=" * 80)
    
    if all_issues:
        print(f"发现 {len(all_issues)} 个潜在问题:")
        for i, issue in enumerate(all_issues, 1):
            print(f"{i}. {issue}")
    else:
        print("✅ 没有发现明显问题")
    
    # 提供改进建议
    suggest_improvements()
    
    # 提供立即修复方案
    print("=" * 80)
    print("🚀 立即修复方案")
    print("=" * 80)
    
    print("1. 创建改进的清理机制")
    print("2. 添加进程跟踪功能")
    print("3. 实现同步清理备选方案")
    print("4. 增强进程识别逻辑")
    
    choice = input("\n是否创建修复代码？(y/n): ").lower().strip()
    if choice == 'y':
        print("正在创建修复代码...")
        return True
    else:
        print("诊断完成")
        return False

if __name__ == "__main__":
    try:
        create_fix = main()
        if create_fix:
            print("请等待修复代码生成...")
    except KeyboardInterrupt:
        print("\n用户中断诊断")
    except Exception as e:
        print(f"\n诊断异常: {e}")
        import traceback
        traceback.print_exc()
