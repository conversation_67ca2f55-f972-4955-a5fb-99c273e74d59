#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome启动问题诊断和修复工具
"""

import os
import sys
import time
import psutil
import socket
import subprocess
from pathlib import Path


def check_chrome_processes():
    """检查Chrome进程"""
    print("🔍 检查Chrome进程...")
    
    current_dir = os.getcwd()
    program_processes = []
    personal_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'chrome' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'] or [])
                
                # 检查是否是程序启动的Chrome
                if any(indicator in cmdline for indicator in [
                    'browser_profiles',
                    'account_',
                    'remote-debugging-port=9',
                    current_dir.replace('\\', '/')
                ]):
                    program_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline
                    })
                else:
                    personal_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline
                    })
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print(f"  📊 程序Chrome进程: {len(program_processes)}个")
    for proc in program_processes:
        print(f"    PID {proc['pid']}: {proc['cmdline']}")
    
    print(f"  📊 个人Chrome进程: {len(personal_processes)}个")
    for proc in personal_processes[:3]:  # 只显示前3个
        print(f"    PID {proc['pid']}: {proc['cmdline']}")
    
    return program_processes, personal_processes


def check_chrome_ports():
    """检查Chrome端口占用"""
    print("\n🔍 检查Chrome端口占用...")
    
    # 检查程序使用的端口范围
    program_ports = list(range(9000, 9010))
    occupied_ports = []
    
    for port in program_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            
            if result == 0:
                occupied_ports.append(port)
                print(f"  ⚠️  端口 {port} 被占用")
            else:
                print(f"  ✅ 端口 {port} 可用")
                
        except Exception as e:
            print(f"  ❌ 检查端口 {port} 时出错: {e}")
    
    return occupied_ports


def cleanup_program_chrome():
    """清理程序相关的Chrome进程"""
    print("\n🧹 清理程序Chrome进程...")
    
    current_dir = os.getcwd()
    killed_count = 0
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'chrome' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'] or [])
                
                # 检查是否是程序启动的Chrome
                if any(indicator in cmdline for indicator in [
                    'browser_profiles',
                    'account_',
                    'remote-debugging-port=9',
                    current_dir.replace('\\', '/')
                ]):
                    try:
                        process = psutil.Process(proc.info['pid'])
                        process.terminate()
                        print(f"  ✅ 终止Chrome进程: PID {proc.info['pid']}")
                        killed_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        print(f"  ❌ 无法终止进程: PID {proc.info['pid']}")
                        
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # 等待进程终止
    time.sleep(2)
    
    # 强制杀死残留进程
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'chrome' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'] or [])
                
                if any(indicator in cmdline for indicator in [
                    'browser_profiles',
                    'account_',
                    'remote-debugging-port=9',
                    current_dir.replace('\\', '/')
                ]):
                    try:
                        process = psutil.Process(proc.info['pid'])
                        process.kill()
                        print(f"  🔨 强制杀死Chrome进程: PID {proc.info['pid']}")
                        killed_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                        
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print(f"  📊 清理了 {killed_count} 个Chrome进程")
    return killed_count


def cleanup_chrome_temp_files():
    """清理Chrome临时文件"""
    print("\n🧹 清理Chrome临时文件...")
    
    try:
        # 清理程序的浏览器配置文件
        browser_profiles_dir = Path("data/browser_profiles")
        if browser_profiles_dir.exists():
            for account_dir in browser_profiles_dir.iterdir():
                if account_dir.is_dir():
                    # 清理可能的锁文件
                    lock_files = [
                        account_dir / "SingletonLock",
                        account_dir / "lockfile",
                        account_dir / "Default" / "lockfile"
                    ]
                    
                    for lock_file in lock_files:
                        if lock_file.exists():
                            try:
                                lock_file.unlink()
                                print(f"  ✅ 删除锁文件: {lock_file}")
                            except Exception as e:
                                print(f"  ❌ 删除锁文件失败: {lock_file}, {e}")
        
        print("  ✅ Chrome临时文件清理完成")
        
    except Exception as e:
        print(f"  ❌ 清理Chrome临时文件失败: {e}")


def test_chrome_startup():
    """测试Chrome启动"""
    print("\n🧪 测试Chrome启动...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # 创建测试选项
        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--remote-debugging-port=9999")
        
        print("  🚀 尝试启动Chrome...")
        start_time = time.time()
        
        try:
            service = Service()
            driver = webdriver.Chrome(service=service, options=options)
            
            end_time = time.time()
            startup_time = end_time - start_time
            
            print(f"  ✅ Chrome启动成功！耗时: {startup_time:.2f}秒")
            
            # 测试基本功能
            driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")
            title = driver.title
            print(f"  ✅ Chrome功能正常，页面标题: {title}")
            
            driver.quit()
            print("  ✅ Chrome正常关闭")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Chrome启动失败: {e}")
            return False
            
    except ImportError as e:
        print(f"  ❌ 导入Selenium失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 Chrome启动问题诊断和修复工具")
    print("=" * 50)
    
    # 步骤1: 检查当前状态
    print("📋 步骤1: 检查当前状态")
    program_processes, personal_processes = check_chrome_processes()
    occupied_ports = check_chrome_ports()
    
    # 步骤2: 清理环境
    print("\n📋 步骤2: 清理环境")
    if program_processes or occupied_ports:
        cleanup_program_chrome()
        cleanup_chrome_temp_files()
        time.sleep(3)  # 等待清理完成
    else:
        print("✅ 环境已经干净，无需清理")
    
    # 步骤3: 测试Chrome启动
    print("\n📋 步骤3: 测试Chrome启动")
    success = test_chrome_startup()
    
    # 步骤4: 总结
    print("\n📋 步骤4: 诊断总结")
    if success:
        print("✅ Chrome启动测试成功！")
        print("💡 建议:")
        print("   1. 重新启动你的程序")
        print("   2. 如果问题仍然存在，请检查防火墙设置")
        print("   3. 确保Chrome浏览器已正确安装")
    else:
        print("❌ Chrome启动测试失败！")
        print("💡 建议:")
        print("   1. 重新安装Chrome浏览器")
        print("   2. 检查系统权限设置")
        print("   3. 重启计算机后再试")
        print("   4. 检查是否有杀毒软件阻止Chrome启动")
    
    print("\n🎯 诊断完成！")


if __name__ == "__main__":
    main()
