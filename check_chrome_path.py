#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Chrome启动路径和配置
"""

import os
import sys
import winreg
import subprocess
from pathlib import Path
import psutil

def get_chrome_path_from_registry():
    """从注册表获取Chrome路径"""
    print("1. 从注册表查找Chrome路径...")
    
    registry_paths = [
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"),
        (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Google\Chrome\BLBeacon"),
        (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Google\Chrome\BLBeacon"),
    ]
    
    chrome_paths = []
    
    for hkey, subkey in registry_paths:
        try:
            with winreg.OpenKey(hkey, subkey) as key:
                try:
                    path, _ = winreg.QueryValueEx(key, "")
                    if path and os.path.exists(path):
                        chrome_paths.append(("注册表默认值", path))
                        print(f"  ✅ 找到: {path}")
                except FileNotFoundError:
                    pass
                
                try:
                    path, _ = winreg.QueryValueEx(key, "Path")
                    if path and os.path.exists(path):
                        chrome_exe = os.path.join(path, "chrome.exe")
                        if os.path.exists(chrome_exe):
                            chrome_paths.append(("注册表Path值", chrome_exe))
                            print(f"  ✅ 找到: {chrome_exe}")
                except FileNotFoundError:
                    pass
                    
        except FileNotFoundError:
            continue
        except Exception as e:
            print(f"  ⚠️  检查注册表 {subkey} 失败: {e}")
    
    return chrome_paths

def get_chrome_path_from_common_locations():
    """从常见位置查找Chrome"""
    print("\n2. 从常见位置查找Chrome...")
    
    common_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME')),
        r"C:\Program Files\Google\Chrome Beta\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome Beta\Application\chrome.exe",
    ]
    
    chrome_paths = []
    
    for path in common_paths:
        if os.path.exists(path):
            chrome_paths.append(("常见位置", path))
            print(f"  ✅ 找到: {path}")
    
    return chrome_paths

def get_chrome_path_from_processes():
    """从运行中的进程获取Chrome路径"""
    print("\n3. 从运行中的Chrome进程获取路径...")
    
    chrome_paths = []
    
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            if proc.info['name'] and 'chrome.exe' in proc.info['name'].lower():
                exe_path = proc.info['exe']
                if exe_path and exe_path not in [path[1] for path in chrome_paths]:
                    chrome_paths.append(("运行进程", exe_path))
                    print(f"  ✅ 找到运行中的Chrome: {exe_path}")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return chrome_paths

def get_chrome_version(chrome_path):
    """获取Chrome版本"""
    try:
        result = subprocess.run([chrome_path, "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            return "无法获取版本"
    except Exception as e:
        return f"获取版本失败: {e}"

def check_chromedriver():
    """检查ChromeDriver"""
    print("\n4. 检查ChromeDriver...")
    
    try:
        from selenium.webdriver.chrome.service import Service
        service = Service()
        driver_path = service.path
        print(f"  Selenium默认ChromeDriver路径: {driver_path}")
        
        if os.path.exists(driver_path):
            print("  ✅ ChromeDriver存在")
            
            # 获取ChromeDriver版本
            try:
                result = subprocess.run([driver_path, "--version"], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"  版本: {result.stdout.strip()}")
                else:
                    print("  ⚠️  无法获取ChromeDriver版本")
            except Exception as e:
                print(f"  ⚠️  获取ChromeDriver版本失败: {e}")
        else:
            print("  ❌ ChromeDriver不存在")
            
    except ImportError:
        print("  ⚠️  Selenium未安装")
    except Exception as e:
        print(f"  ❌ 检查ChromeDriver失败: {e}")

def check_current_project_chrome_config():
    """检查当前项目的Chrome配置"""
    print("\n5. 检查当前项目Chrome配置...")
    
    # 检查项目中的Chrome相关文件
    config_files = [
        "src/core/browser_manager.py",
        "src/core/chrome_manager.py", 
        "src/config/browser_config.py",
        "config/browser_config.json",
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"  ✅ 找到配置文件: {config_file}")
        else:
            print(f"  ❌ 配置文件不存在: {config_file}")
    
    # 检查用户数据目录
    user_data_dir = Path("data/browser_profiles")
    if user_data_dir.exists():
        print(f"  ✅ 用户数据目录存在: {user_data_dir.absolute()}")
        profiles = list(user_data_dir.iterdir())
        print(f"  📁 发现 {len(profiles)} 个配置文件夹")
        for profile in profiles[:5]:  # 只显示前5个
            print(f"    - {profile.name}")
    else:
        print(f"  ❌ 用户数据目录不存在: {user_data_dir.absolute()}")

def main():
    """主函数"""
    print("🔍 Chrome启动路径检查")
    print("=" * 60)
    
    all_chrome_paths = []
    
    # 从不同来源收集Chrome路径
    all_chrome_paths.extend(get_chrome_path_from_registry())
    all_chrome_paths.extend(get_chrome_path_from_common_locations())
    all_chrome_paths.extend(get_chrome_path_from_processes())
    
    # 去重
    unique_paths = {}
    for source, path in all_chrome_paths:
        if path not in unique_paths:
            unique_paths[path] = source
    
    print(f"\n" + "=" * 60)
    print("📊 Chrome路径汇总")
    print("=" * 60)
    
    if unique_paths:
        print(f"发现 {len(unique_paths)} 个不同的Chrome路径:")
        for i, (path, source) in enumerate(unique_paths.items(), 1):
            print(f"\n{i}. {path}")
            print(f"   来源: {source}")
            print(f"   存在: {'✅' if os.path.exists(path) else '❌'}")
            
            if os.path.exists(path):
                version = get_chrome_version(path)
                print(f"   版本: {version}")
                
                # 检查文件大小
                try:
                    size = os.path.getsize(path) / (1024 * 1024)  # MB
                    print(f"   大小: {size:.1f} MB")
                except Exception:
                    print(f"   大小: 无法获取")
    else:
        print("❌ 未找到任何Chrome路径")
        print("建议:")
        print("  1. 安装Google Chrome浏览器")
        print("  2. 检查Chrome是否正确安装")
        print("  3. 重新安装Chrome")
    
    # 检查其他相关组件
    check_chromedriver()
    check_current_project_chrome_config()
    
    print(f"\n" + "=" * 60)
    print("💡 建议")
    print("=" * 60)
    
    if unique_paths:
        recommended_path = list(unique_paths.keys())[0]
        print(f"推荐使用路径: {recommended_path}")
        print("在Selenium中使用:")
        print(f"  from selenium.webdriver.chrome.service import Service")
        print(f"  service = Service(executable_path=r'{recommended_path}')")
        print(f"  driver = webdriver.Chrome(service=service)")
    else:
        print("请先安装Google Chrome浏览器")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断检查")
    except Exception as e:
        print(f"\n异常: {e}")
        import traceback
        traceback.print_exc()
