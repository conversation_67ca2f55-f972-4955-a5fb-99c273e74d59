#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Chrome清理修复效果
"""

import os
import sys
import time
import psutil
import subprocess
from pathlib import Path

def count_chrome_processes():
    """统计Chrome进程"""
    chrome_processes = {
        'total': 0,
        'program': 0,
        'personal': 0,
        'details': []
    }
    
    current_dir = os.getcwd().replace('\\', '/')
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
        try:
            if not proc.info['name'] or 'chrome' not in proc.info['name'].lower():
                continue
            
            cmdline = ' '.join(proc.info['cmdline'] or [])
            
            # 程序Chrome的特征
            program_indicators = [
                'browser_profiles',
                'account_',
                'remote-debugging-port=9',
                '--user-data-dir=' + current_dir,
                '--disable-blink-features=AutomationControlled'
            ]
            
            # 个人Chrome的特征
            personal_indicators = [
                'Default/User Data',
                'Google/Chrome/User Data',
                '--profile-directory=Default'
            ]
            
            is_program_chrome = any(indicator in cmdline for indicator in program_indicators)
            is_personal_chrome = any(indicator in cmdline for indicator in personal_indicators)
            
            chrome_processes['total'] += 1
            
            process_info = {
                'pid': proc.info['pid'],
                'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline,
                'create_time': proc.info['create_time'],
                'type': 'unknown'
            }
            
            if is_program_chrome and not is_personal_chrome:
                chrome_processes['program'] += 1
                process_info['type'] = 'program'
            elif is_personal_chrome:
                chrome_processes['personal'] += 1
                process_info['type'] = 'personal'
            else:
                process_info['type'] = 'unknown'
            
            chrome_processes['details'].append(process_info)
            
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return chrome_processes

def test_improved_cleanup():
    """测试改进的清理机制"""
    print("🧪 测试改进的Chrome清理机制")
    print("=" * 60)
    
    # 检查清理前的状态
    print("1. 清理前Chrome进程状态:")
    before_cleanup = count_chrome_processes()
    print(f"   总进程数: {before_cleanup['total']}")
    print(f"   程序进程: {before_cleanup['program']}")
    print(f"   个人进程: {before_cleanup['personal']}")
    
    if before_cleanup['program'] > 0:
        print(f"\n   程序Chrome进程详情:")
        for proc in before_cleanup['details']:
            if proc['type'] == 'program':
                age = time.time() - proc['create_time']
                print(f"     PID {proc['pid']}: 存活 {age/60:.1f} 分钟")
                print(f"       命令行: {proc['cmdline']}")
    
    if before_cleanup['program'] == 0:
        print("✅ 没有发现程序Chrome进程残留")
        return True
    
    # 测试清理
    print(f"\n2. 开始测试清理...")
    choice = input(f"发现 {before_cleanup['program']} 个程序Chrome进程，是否测试清理？(y/n): ").lower().strip()
    
    if choice != 'y':
        print("跳过清理测试")
        return False
    
    # 导入并测试清理方法
    try:
        # 模拟主窗口的清理方法
        from src.ui.main_window import MainWindow
        from src.config.settings import Settings
        
        # 创建临时主窗口实例来测试清理方法
        settings = Settings()
        
        # 创建一个简化的清理测试类
        class CleanupTester:
            def __init__(self):
                import logging
                self.logger = logging.getLogger(__name__)
                logging.basicConfig(level=logging.INFO)
            
            def _count_remaining_program_chrome(self):
                """统计剩余的程序Chrome进程数量"""
                try:
                    import psutil
                    import os
                    
                    current_dir = os.getcwd()
                    count = 0
                    
                    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                        try:
                            if not proc.info['name'] or 'chrome' not in proc.info['name'].lower():
                                continue
                            
                            cmdline = ' '.join(proc.info['cmdline'] or [])
                            
                            # 检查是否是程序Chrome
                            if any(indicator in cmdline for indicator in [
                                'browser_profiles',
                                'account_',
                                'remote-debugging-port=9',
                                current_dir.replace('\\', '/')
                            ]):
                                # 确保不是个人Chrome
                                if not any(indicator in cmdline for indicator in [
                                    'Default/User Data',
                                    'Google/Chrome/User Data',
                                    '--profile-directory=Default'
                                ]):
                                    count += 1
                        
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue
                    
                    return count
                    
                except Exception:
                    return 0
            
            def test_cleanup(self, force=False):
                """测试清理方法"""
                try:
                    import psutil
                    import os
                    import time

                    current_dir = os.getcwd()
                    cleaned_count = 0
                    timeout = 10 if not force else 5

                    self.logger.info(f"开始{'强制' if force else '温和'}清理Chrome进程...")
                    start_time = time.time()

                    # 查找所有程序Chrome进程
                    program_chrome_processes = []
                    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                        try:
                            if not proc.info['name'] or 'chrome' not in proc.info['name'].lower():
                                continue
                            
                            cmdline = ' '.join(proc.info['cmdline'] or [])
                            
                            # 程序Chrome的特征
                            program_indicators = [
                                'browser_profiles',
                                'account_',
                                'remote-debugging-port=9',
                                '--user-data-dir=' + current_dir.replace('\\', '/'),
                                '--disable-blink-features=AutomationControlled'
                            ]
                            
                            # 个人Chrome的特征（需要保护）
                            personal_indicators = [
                                'Default/User Data',
                                'Google/Chrome/User Data',
                                '--profile-directory=Default'
                            ]
                            
                            is_program_chrome = any(indicator in cmdline for indicator in program_indicators)
                            is_personal_chrome = any(indicator in cmdline for indicator in personal_indicators)
                            
                            # 只清理程序Chrome，保护个人Chrome
                            if is_program_chrome and not is_personal_chrome:
                                program_chrome_processes.append(proc)

                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue

                    self.logger.info(f"发现 {len(program_chrome_processes)} 个程序Chrome进程需要清理")

                    # 清理进程
                    for proc in program_chrome_processes:
                        try:
                            if time.time() - start_time > timeout:
                                self.logger.warning("清理超时，切换到强制模式")
                                force = True
                            
                            if force:
                                # 强制杀死
                                proc.kill()
                                self.logger.info(f"强制杀死Chrome进程: PID {proc.info['pid']}")
                            else:
                                # 温和终止
                                proc.terminate()
                                # 等待进程退出
                                try:
                                    proc.wait(timeout=3)
                                except psutil.TimeoutExpired:
                                    # 如果3秒内没有退出，强制kill
                                    proc.kill()
                                self.logger.info(f"清理Chrome进程: PID {proc.info['pid']}")
                            
                            cleaned_count += 1

                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            # 进程已经不存在或无权限，算作成功
                            cleaned_count += 1
                            continue
                        except Exception as e:
                            self.logger.warning(f"清理Chrome进程失败 PID {proc.info['pid']}: {e}")

                    # 等待进程真正退出
                    if cleaned_count > 0:
                        time.sleep(2)
                        
                        # 验证清理效果
                        remaining = self._count_remaining_program_chrome()
                        if remaining > 0:
                            self.logger.warning(f"仍有 {remaining} 个程序Chrome进程残留")
                            if not force:
                                # 如果还有残留且不是强制模式，再次尝试强制清理
                                self.logger.info("尝试强制清理残留进程...")
                                return self.test_cleanup(force=True)
                            return False
                        else:
                            self.logger.info(f"成功清理了 {cleaned_count} 个Chrome进程")
                            return True
                    else:
                        self.logger.info("没有发现需要清理的Chrome进程")
                        return True

                except Exception as e:
                    self.logger.error(f"Chrome进程清理失败: {e}")
                    return False
        
        # 执行清理测试
        tester = CleanupTester()
        cleanup_success = tester.test_cleanup(force=False)
        
        print(f"\n3. 清理结果: {'成功' if cleanup_success else '失败'}")
        
        # 检查清理后的状态
        time.sleep(1)
        after_cleanup = count_chrome_processes()
        print(f"\n4. 清理后Chrome进程状态:")
        print(f"   总进程数: {after_cleanup['total']}")
        print(f"   程序进程: {after_cleanup['program']}")
        print(f"   个人进程: {after_cleanup['personal']}")
        
        # 验证清理效果
        if after_cleanup['program'] == 0:
            print("✅ 清理成功！所有程序Chrome进程已被清理")
            return True
        else:
            print(f"⚠️  仍有 {after_cleanup['program']} 个程序Chrome进程残留")
            return False
        
    except Exception as e:
        print(f"❌ 清理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 Chrome清理修复效果测试")
    print("=" * 80)
    
    success = test_improved_cleanup()
    
    print(f"\n" + "=" * 80)
    print("📊 测试总结")
    print("=" * 80)
    
    if success:
        print("✅ Chrome清理修复测试通过")
        print("   - 程序Chrome进程能够被正确识别")
        print("   - 个人Chrome进程得到保护")
        print("   - 清理机制工作正常")
    else:
        print("❌ Chrome清理修复测试失败")
        print("   - 可能需要进一步调试清理逻辑")
        print("   - 建议检查进程识别条件")
    
    return success

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
