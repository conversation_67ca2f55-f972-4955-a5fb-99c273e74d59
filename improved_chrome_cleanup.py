#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的Chrome进程清理机制
解决程序退出时Chrome进程残留问题
"""

import os
import sys
import time
import psutil
import threading
import asyncio
import signal
import atexit
from pathlib import Path
from typing import Set, List, Dict, Any
import logging

class ChromeProcessTracker:
    """Chrome进程跟踪器 - 记录程序启动的Chrome进程"""
    
    def __init__(self):
        self.tracked_processes: Set[int] = set()
        self.process_info: Dict[int, Dict[str, Any]] = {}
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # 注册退出清理
        atexit.register(self.emergency_cleanup)
        
        # 注册信号处理
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, self._signal_handler)
        if hasattr(signal, 'SIGINT'):
            signal.signal(signal.SIGINT, self._signal_handler)
    
    def track_process(self, pid: int, account_id: int = None, debug_port: int = None):
        """跟踪Chrome进程"""
        with self.lock:
            self.tracked_processes.add(pid)
            self.process_info[pid] = {
                'account_id': account_id,
                'debug_port': debug_port,
                'start_time': time.time(),
                'tracked_time': time.time()
            }
            self.logger.debug(f"开始跟踪Chrome进程: PID {pid}")
    
    def untrack_process(self, pid: int):
        """停止跟踪Chrome进程"""
        with self.lock:
            self.tracked_processes.discard(pid)
            self.process_info.pop(pid, None)
            self.logger.debug(f"停止跟踪Chrome进程: PID {pid}")
    
    def get_tracked_processes(self) -> List[int]:
        """获取所有被跟踪的进程"""
        with self.lock:
            return list(self.tracked_processes)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，开始清理...")
        self.emergency_cleanup()
        sys.exit(0)
    
    def emergency_cleanup(self):
        """紧急清理所有跟踪的进程"""
        tracked = self.get_tracked_processes()
        if tracked:
            self.logger.info(f"紧急清理 {len(tracked)} 个跟踪的Chrome进程")
            for pid in tracked:
                try:
                    proc = psutil.Process(pid)
                    proc.terminate()
                    self.logger.debug(f"紧急终止Chrome进程: PID {pid}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

# 全局进程跟踪器
_process_tracker = ChromeProcessTracker()

class ImprovedChromeCleanup:
    """改进的Chrome清理机制"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.cleanup_timeout = 15  # 总清理超时时间
        self.force_cleanup_timeout = 5  # 强制清理超时时间
    
    def cleanup_on_exit(self, force: bool = False) -> bool:
        """程序退出时的清理方法"""
        self.logger.info("开始程序退出清理...")
        
        start_time = time.time()
        success = False
        
        try:
            if force:
                # 强制清理模式
                success = self._force_cleanup_all_chrome()
            else:
                # 温和清理模式
                success = self._gentle_cleanup_chrome()
                
                # 如果温和清理失败，尝试强制清理
                if not success:
                    self.logger.warning("温和清理失败，尝试强制清理...")
                    success = self._force_cleanup_all_chrome()
            
            elapsed = time.time() - start_time
            self.logger.info(f"清理完成，耗时 {elapsed:.2f}秒，成功: {success}")
            
        except Exception as e:
            self.logger.error(f"清理过程异常: {e}")
            # 最后的紧急清理
            self._emergency_system_cleanup()
        
        return success
    
    def _gentle_cleanup_chrome(self) -> bool:
        """温和清理Chrome进程"""
        try:
            # 1. 清理跟踪的进程
            tracked_processes = _process_tracker.get_tracked_processes()
            self.logger.info(f"清理 {len(tracked_processes)} 个跟踪的Chrome进程")
            
            cleaned_count = 0
            for pid in tracked_processes:
                if self._terminate_process_gently(pid):
                    cleaned_count += 1
                    _process_tracker.untrack_process(pid)
            
            # 2. 清理程序特征的Chrome进程
            program_processes = self._find_program_chrome_processes()
            self.logger.info(f"发现 {len(program_processes)} 个程序Chrome进程")
            
            for proc_info in program_processes:
                if self._terminate_process_gently(proc_info['pid']):
                    cleaned_count += 1
            
            self.logger.info(f"温和清理完成，清理了 {cleaned_count} 个进程")
            
            # 3. 验证清理效果
            remaining = self._find_program_chrome_processes()
            return len(remaining) == 0
            
        except Exception as e:
            self.logger.error(f"温和清理失败: {e}")
            return False
    
    def _force_cleanup_all_chrome(self) -> bool:
        """强制清理所有程序Chrome进程"""
        try:
            self.logger.info("开始强制清理...")
            
            # 1. 强制清理跟踪的进程
            tracked_processes = _process_tracker.get_tracked_processes()
            for pid in tracked_processes:
                self._kill_process_forcefully(pid)
                _process_tracker.untrack_process(pid)
            
            # 2. 强制清理程序特征的进程
            program_processes = self._find_program_chrome_processes()
            cleaned_count = 0
            
            for proc_info in program_processes:
                if self._kill_process_forcefully(proc_info['pid']):
                    cleaned_count += 1
            
            self.logger.info(f"强制清理完成，清理了 {cleaned_count} 个进程")
            
            # 3. 等待进程真正退出
            time.sleep(2)
            
            # 4. 验证清理效果
            remaining = self._find_program_chrome_processes()
            if remaining:
                self.logger.warning(f"仍有 {len(remaining)} 个程序Chrome进程残留")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"强制清理失败: {e}")
            return False
    
    def _find_program_chrome_processes(self) -> List[Dict[str, Any]]:
        """查找程序启动的Chrome进程"""
        program_processes = []
        current_dir = os.getcwd().replace('\\', '/')
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
            try:
                if not proc.info['name'] or 'chrome' not in proc.info['name'].lower():
                    continue
                
                cmdline = ' '.join(proc.info['cmdline'] or [])
                
                # 程序Chrome的特征
                program_indicators = [
                    'browser_profiles',
                    'account_',
                    'remote-debugging-port=9',
                    '--user-data-dir=' + current_dir,
                    '--disable-blink-features=AutomationControlled',
                    '--test-type'
                ]
                
                # 个人Chrome的特征（需要保护）
                personal_indicators = [
                    'Default/User Data',
                    'Google/Chrome/User Data',
                    '--profile-directory=Default'
                ]
                
                # 检查是否是程序Chrome
                is_program_chrome = any(indicator in cmdline for indicator in program_indicators)
                is_personal_chrome = any(indicator in cmdline for indicator in personal_indicators)
                
                # 只处理程序Chrome，保护个人Chrome
                if is_program_chrome and not is_personal_chrome:
                    program_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': cmdline,
                        'create_time': proc.info['create_time']
                    })
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return program_processes
    
    def _terminate_process_gently(self, pid: int) -> bool:
        """温和地终止进程"""
        try:
            proc = psutil.Process(pid)
            
            # 首先尝试terminate
            proc.terminate()
            
            # 等待进程退出
            try:
                proc.wait(timeout=3)
                self.logger.debug(f"温和终止进程成功: PID {pid}")
                return True
            except psutil.TimeoutExpired:
                # 如果3秒内没有退出，强制kill
                proc.kill()
                self.logger.debug(f"强制终止进程: PID {pid}")
                return True
                
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            # 进程已经不存在或无权限
            return True
        except Exception as e:
            self.logger.warning(f"终止进程失败 PID {pid}: {e}")
            return False
    
    def _kill_process_forcefully(self, pid: int) -> bool:
        """强制杀死进程"""
        try:
            proc = psutil.Process(pid)
            proc.kill()
            
            # 等待确认进程死亡
            try:
                proc.wait(timeout=2)
            except psutil.TimeoutExpired:
                pass  # 进程可能已经死亡
            
            self.logger.debug(f"强制杀死进程: PID {pid}")
            return True
            
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return True
        except Exception as e:
            self.logger.warning(f"强制杀死进程失败 PID {pid}: {e}")
            return False
    
    def _emergency_system_cleanup(self):
        """紧急系统级清理"""
        try:
            self.logger.warning("执行紧急系统级清理...")
            
            if os.name == 'nt':  # Windows
                import subprocess
                
                # 使用taskkill强制终止Chrome进程
                # 只终止包含特定参数的Chrome进程
                try:
                    # 获取所有Chrome进程的详细信息
                    result = subprocess.run([
                        'wmic', 'process', 'where', 'name="chrome.exe"', 
                        'get', 'ProcessId,CommandLine', '/format:csv'
                    ], capture_output=True, text=True, timeout=10)
                    
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines[1:]:  # 跳过标题行
                            if 'browser_profiles' in line or 'remote-debugging-port=9' in line:
                                # 提取PID
                                parts = line.split(',')
                                if len(parts) >= 3:
                                    try:
                                        pid = int(parts[2])
                                        subprocess.run(['taskkill', '/F', '/PID', str(pid)], 
                                                     capture_output=True, timeout=5)
                                        self.logger.debug(f"系统级清理进程: PID {pid}")
                                    except (ValueError, subprocess.TimeoutExpired):
                                        continue
                
                except subprocess.TimeoutExpired:
                    self.logger.warning("系统级清理超时")
                except Exception as e:
                    self.logger.warning(f"系统级清理失败: {e}")
            
        except Exception as e:
            self.logger.error(f"紧急系统级清理异常: {e}")

# 创建全局清理器实例
_chrome_cleanup = ImprovedChromeCleanup()

def register_chrome_process(pid: int, account_id: int = None, debug_port: int = None):
    """注册Chrome进程用于跟踪"""
    _process_tracker.track_process(pid, account_id, debug_port)

def unregister_chrome_process(pid: int):
    """取消注册Chrome进程"""
    _process_tracker.untrack_process(pid)

def cleanup_chrome_on_exit(force: bool = False) -> bool:
    """程序退出时清理Chrome进程"""
    return _chrome_cleanup.cleanup_on_exit(force)

def test_cleanup():
    """测试清理功能"""
    print("🧪 测试改进的Chrome清理机制...")
    
    # 查找当前程序Chrome进程
    cleanup = ImprovedChromeCleanup()
    program_processes = cleanup._find_program_chrome_processes()
    
    print(f"发现 {len(program_processes)} 个程序Chrome进程")
    for proc in program_processes:
        print(f"  PID {proc['pid']}: {proc['cmdline'][:100]}...")
    
    if program_processes:
        choice = input("是否测试清理这些进程？(y/n): ").lower().strip()
        if choice == 'y':
            success = cleanup.cleanup_on_exit(force=False)
            print(f"清理结果: {'成功' if success else '失败'}")
    else:
        print("✅ 没有发现需要清理的程序Chrome进程")

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    test_cleanup()
