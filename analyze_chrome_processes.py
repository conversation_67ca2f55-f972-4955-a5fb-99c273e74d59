#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析Chrome进程
"""

import os
import psutil
import subprocess
from datetime import datetime
from pathlib import Path

def analyze_chrome_processes():
    """详细分析所有Chrome进程"""
    print("🔍 详细Chrome进程分析")
    print("=" * 80)
    
    chrome_processes = []
    
    # 收集所有Chrome进程信息
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status', 'create_time', 'memory_info', 'cpu_percent']):
        try:
            if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                # 获取详细信息
                cmdline = proc.info['cmdline'] or []
                cmdline_str = ' '.join(cmdline)
                
                # 获取创建时间
                create_time = datetime.fromtimestamp(proc.info['create_time'])
                
                # 获取内存使用
                memory_mb = proc.info['memory_info'].rss / (1024 * 1024) if proc.info['memory_info'] else 0
                
                process_info = {
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'status': proc.info['status'],
                    'cmdline': cmdline_str,
                    'create_time': create_time,
                    'memory_mb': memory_mb,
                    'cpu_percent': proc.info['cpu_percent']
                }
                
                chrome_processes.append(process_info)
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    if not chrome_processes:
        print("✅ 没有发现Chrome进程")
        return
    
    print(f"发现 {len(chrome_processes)} 个Chrome进程:")
    print()
    
    # 按创建时间排序
    chrome_processes.sort(key=lambda x: x['create_time'])
    
    # 分类分析
    main_processes = []      # 主进程
    renderer_processes = []  # 渲染进程
    utility_processes = []   # 工具进程
    program_processes = []   # 程序相关进程
    unknown_processes = []   # 未知进程
    
    for i, proc in enumerate(chrome_processes, 1):
        print(f"进程 {i}:")
        print(f"  PID: {proc['pid']}")
        print(f"  名称: {proc['name']}")
        print(f"  状态: {proc['status']}")
        print(f"  创建时间: {proc['create_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  内存使用: {proc['memory_mb']:.1f} MB")
        print(f"  CPU使用: {proc['cpu_percent']:.1f}%")
        
        # 分析命令行参数
        cmdline = proc['cmdline']
        print(f"  命令行: {cmdline[:100]}{'...' if len(cmdline) > 100 else ''}")
        
        # 进程分类
        if '--type=' not in cmdline:
            main_processes.append(proc)
            print("  🏠 类型: 主进程 (浏览器主窗口)")
        elif '--type=renderer' in cmdline:
            renderer_processes.append(proc)
            print("  🎨 类型: 渲染进程 (网页标签页)")
        elif '--type=utility' in cmdline:
            utility_processes.append(proc)
            print("  🔧 类型: 工具进程 (后台服务)")
        elif '--type=gpu-process' in cmdline:
            utility_processes.append(proc)
            print("  🎮 类型: GPU进程 (图形加速)")
        elif '--type=broker' in cmdline:
            utility_processes.append(proc)
            print("  🔒 类型: 代理进程 (安全沙箱)")
        else:
            unknown_processes.append(proc)
            print("  ❓ 类型: 未知进程")
        
        # 检查是否是程序相关的Chrome
        if any(keyword in cmdline for keyword in ['browser_profiles', 'remote-debugging-port=9000', 'user-data-dir']):
            program_processes.append(proc)
            print("  ⚠️  这是程序启动的Chrome进程!")
            
            # 分析用户数据目录
            if 'user-data-dir' in cmdline:
                for part in cmdline.split():
                    if part.startswith('--user-data-dir='):
                        user_data_dir = part.split('=', 1)[1]
                        print(f"    📁 用户数据目录: {user_data_dir}")
                        break
            
            # 分析调试端口
            if 'remote-debugging-port' in cmdline:
                for part in cmdline.split():
                    if part.startswith('--remote-debugging-port='):
                        debug_port = part.split('=', 1)[1]
                        print(f"    🔌 调试端口: {debug_port}")
                        break
        
        print()
    
    # 汇总分析
    print("=" * 80)
    print("📊 进程分类汇总")
    print("=" * 80)
    
    print(f"🏠 主进程: {len(main_processes)} 个")
    for proc in main_processes:
        print(f"  - PID {proc['pid']}: {proc['memory_mb']:.1f}MB")
    
    print(f"\n🎨 渲染进程: {len(renderer_processes)} 个")
    for proc in renderer_processes:
        print(f"  - PID {proc['pid']}: {proc['memory_mb']:.1f}MB")
    
    print(f"\n🔧 工具进程: {len(utility_processes)} 个")
    for proc in utility_processes:
        print(f"  - PID {proc['pid']}: {proc['memory_mb']:.1f}MB")
    
    if program_processes:
        print(f"\n⚠️  程序相关进程: {len(program_processes)} 个")
        for proc in program_processes:
            print(f"  - PID {proc['pid']}: {proc['memory_mb']:.1f}MB")
    
    if unknown_processes:
        print(f"\n❓ 未知进程: {len(unknown_processes)} 个")
        for proc in unknown_processes:
            print(f"  - PID {proc['pid']}: {proc['memory_mb']:.1f}MB")
    
    # 总内存使用
    total_memory = sum(proc['memory_mb'] for proc in chrome_processes)
    print(f"\n💾 总内存使用: {total_memory:.1f} MB")
    
    # 分析异常情况
    print("\n" + "=" * 80)
    print("🚨 异常分析")
    print("=" * 80)
    
    issues = []
    
    # 检查是否有过多的主进程
    if len(main_processes) > 2:
        issues.append(f"主进程过多: {len(main_processes)}个 (正常应该1-2个)")
    
    # 检查是否有程序残留进程
    if program_processes:
        issues.append(f"发现程序残留进程: {len(program_processes)}个")
    
    # 检查内存使用是否过高
    if total_memory > 1000:  # 超过1GB
        issues.append(f"内存使用过高: {total_memory:.1f}MB")
    
    # 检查是否有僵尸进程
    zombie_processes = [proc for proc in chrome_processes if proc['status'] == 'zombie']
    if zombie_processes:
        issues.append(f"发现僵尸进程: {len(zombie_processes)}个")
    
    if issues:
        print("发现以下问题:")
        for issue in issues:
            print(f"  ❌ {issue}")
        
        print(f"\n🔧 建议解决方案:")
        if program_processes:
            print("  1. 关闭程序相关的Chrome进程:")
            for proc in program_processes:
                print(f"     taskkill /PID {proc['pid']} /F")
        
        if len(main_processes) > 2:
            print("  2. 关闭多余的Chrome主进程 (保留最新的)")
            for proc in main_processes[:-1]:  # 保留最后一个
                print(f"     taskkill /PID {proc['pid']} /F")
        
        print("  3. 重启Chrome浏览器")
        print("  4. 如果问题持续，重启计算机")
        
    else:
        print("✅ 没有发现明显异常")
        print("Chrome进程状态正常")
    
    return chrome_processes, program_processes

def check_chrome_cleanup_needed():
    """检查是否需要清理Chrome"""
    print("\n" + "=" * 80)
    print("🧹 清理建议")
    print("=" * 80)
    
    # 检查用户数据目录中的锁文件
    user_data_dirs = [
        Path("data/browser_profiles"),
        Path(os.path.expanduser("~/AppData/Local/Google/Chrome/User Data"))
    ]
    
    lock_files_found = []
    
    for user_data_dir in user_data_dirs:
        if user_data_dir.exists():
            print(f"检查目录: {user_data_dir}")
            
            # 查找锁文件
            lock_patterns = ["*LOCK*", "*.lock", "*lockfile*"]
            for pattern in lock_patterns:
                lock_files = list(user_data_dir.rglob(pattern))
                lock_files_found.extend(lock_files)
            
            if lock_files_found:
                print(f"  ⚠️  发现 {len(lock_files_found)} 个锁文件")
            else:
                print(f"  ✅ 没有锁文件")
    
    if lock_files_found:
        print(f"\n发现锁文件，建议清理:")
        for lock_file in lock_files_found[:5]:  # 只显示前5个
            print(f"  - {lock_file}")
        if len(lock_files_found) > 5:
            print(f"  ... 还有 {len(lock_files_found) - 5} 个")

def main():
    """主函数"""
    try:
        chrome_processes, program_processes = analyze_chrome_processes()
        check_chrome_cleanup_needed()
        
        # 提供交互式清理选项
        if program_processes:
            print(f"\n" + "=" * 80)
            print("🔧 交互式清理")
            print("=" * 80)
            
            choice = input(f"发现 {len(program_processes)} 个程序相关的Chrome进程，是否清理？(y/n): ").lower().strip()
            if choice == 'y':
                print("正在清理程序相关的Chrome进程...")
                for proc in program_processes:
                    try:
                        process = psutil.Process(proc['pid'])
                        process.terminate()
                        print(f"  ✅ 终止进程 PID {proc['pid']}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                        print(f"  ❌ 无法终止进程 PID {proc['pid']}: {e}")
                
                print("清理完成！建议等待5秒后重新启动程序。")
            else:
                print("跳过清理")
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
